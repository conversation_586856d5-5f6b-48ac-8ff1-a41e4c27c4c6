﻿
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Database.IRepositories;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Database.Repositories
{
    /// <summary>
    /// Use to operate on database to query and save the data for Assessment desk review response
    /// </summary>
    public class AssessmentDRResponseRepository : Repository<AssessmentDRResponse>, IAssessmentDRResponseRepository
    {
        private readonly IDbManager _dbManager;

        public AssessmentDRResponseRepository(IDbManager dbManager, MalariaDbContext dbContext) : base(dbContext)
        {
            _dbManager = dbManager;
        }

        /// <summary>
        /// Get response json for the indicator and strategy
        /// </summary>
        /// <param name="assessmentIndicatorId">Indicator id associated with the assessment</param>
        /// <param name="assessmentStrategyId">Strategy id associated with the assessment</param>
        /// <returns>JSON object</returns>
        public JObject GetResponse(Guid assessmentIndicatorId, Guid assessmentStrategyId)
        {
            string sql = $@"SELECT ResponseJson FROM {MalariaSchemas.DeskReview}.[AssessmentDRResponse]
                            WHERE AssessmentIndicatorId = @AssessmentIndicatorId AND AssessmentStrategyId = @AssessmentStrategyId";

            string responseJson = _dbManager.QuerySingleOrDefault<string>(sql, new { AssessmentIndicatorId = assessmentIndicatorId, AssessmentStrategyId = assessmentStrategyId });

            // Debug logging for P.vivax issue
            Serilog.Log.Information("=== BACKEND RETRIEVE DEBUG ===");
            Serilog.Log.Information("Retrieved JSON from DB: {ResponseJson}", responseJson);

            return responseJson == null ? null : JObject.Parse(responseJson);
        }

        /// <summary>
        /// Get met not met status of desk review indicators  
        /// </summary>
        /// <param name="assessmentId">Assessment id for which score card data are to be fetched</param>      
        /// <returns>List of met not met status of desk review indicators</returns>
        public async Task<IEnumerable<Domain.Dtos.IndicatorMetNotMetDto>> GetResponseStatusAsync(Guid assessmentId)
        {
            string query = $@"SELECT                                      
                                       AI.IndicatorId As IndicatorId,
                                       AST.StrategyId As StrategyId,                                 
                                       CASE WHEN ADRR.CannotBeAssessed = 1
									   THEN {(int)MetNotMetStatus.NotAssessed}
									   ELSE ADRR.MetNotMetStatus
									   END
									   As MetNotMetStatus
                                       FROM ScopeDefinition.Assessment A                                  
                                       INNER JOIN ScopeDefinition.AssessmentStrategy AST ON AST.AssessmentId =A.Id 
                                       INNER JOIN ScopeDefinition.[AssessmentIndicator] AI ON AI.AssessmentId = A.Id                                      
                                       INNER JOIN DeskReview.AssessmentDRResponse ADRR ON ADRR.AssessmentIndicatorId = AI.Id AND ADRR.AssessmentStrategyId = AST.Id 
                                       WHERE A.Id = @AssessmentId";

            IEnumerable<Domain.Dtos.IndicatorMetNotMetDto> deskReviewIndicatorDetails = await _dbManager.QueryAsync<Domain.Dtos.IndicatorMetNotMetDto>(query, new
            {
                AssessmentId = assessmentId,
                IndicatorPriority = (int)Priority.Priority
            });

            return deskReviewIndicatorDetails;
        }

        /// <summary>
        /// Get assessment indicator id based on assessmentId and indicatorId
        /// </summary>
        /// <param name="assessmentId">Current assessmentId</param>
        /// <param name="indicatorId">IndicatorId</param>
        /// <returns>Returns assessmentIndicatorId based on assessmentId and indicatorId</returns>
        public Guid GetAssessmentIndicatorIdByAssesmentIdAndIndicatorId(Guid assessmentId, Guid indicatorId)
        {
            string sql = @$"SELECT Id 
                            FROM {MalariaSchemas.ScopeDefinition}.[AssessmentIndicator]
                            WHERE 
                            AssessmentId = @AssessmentId AND	
                            IndicatorId = @IndicatorId";

            Guid result = _dbManager.QuerySingleOrDefault<Guid>(sql, new { AssessmentId = assessmentId, IndicatorId = indicatorId });

            return result;
        }

        /// <summary>
        /// Get desk review response and document
        /// </summary>
        /// <param name="assessmentId">Assessment id for which desk review response are to be fetched</param>
        /// <param name="strategyId">StrategyId id for which desk review response are to be fetched</param>
        /// <returns>Collection of desk review response by assessment id and strategy id</returns>
        public async Task<IEnumerable<AssessmentDeskReviewResponseDto>> GetAssessmentResponseByAssessmentIdAndStrategyId(Guid assessmentId, Guid strategyId)
        {
            string query = $@"SELECT
                                     A.Id AS AssessmentId, 
                                     ADRR.AssessmentIndicatorId,
                                     ADRR.AssessmentStrategyId, 
                                     ADRR.ResponseJson,
                                     DOC.FileName,
                                     DOC.Id AS DocumentId,
                                     I.Sequence AS IndicatorSequence
                                     FROM 
                                     ScopeDefinition.Assessment A
                                     JOIN {MalariaSchemas.ScopeDefinition}.AssessmentIndicator AI ON AI.AssessmentId = A.Id
                                     JOIN {MalariaSchemas.ScopeDefinition}.AssessmentStrategy AST ON AST.AssessmentId = A.Id 
                                     JOIN {MalariaSchemas.Internal}.Indicator I ON I.Id = AI.IndicatorId
                                     JOIN {MalariaSchemas.Internal}.AnalyticalOutputIndicatorStrategyMapping AISM ON AI.IndicatorId = AISM.IndicatorId AND AST.StrategyId = AISM.StrategyId 
                                     JOIN {MalariaSchemas.DeskReview}.AssessmentDRResponse ADRR ON ADRR.AssessmentIndicatorId = AI.Id AND ADRR.AssessmentStrategyId = AST.Id AND ADRR.CannotBeAssessed = 0
                                     LEFT JOIN {MalariaSchemas.DeskReview}.ResponseDocument DOC  ON DOC.AssessmentDRResponseId = ADRR.Id 
                                     WHERE A.Id= @AssessmentId and AST.StrategyId = @StrategyId";


            IEnumerable<AssessmentDeskReviewResponseDto> analyticalOutputs = await _dbManager.QueryAsync<AssessmentDeskReviewResponseDto>(query, new
            {
                AssessmentId = assessmentId,
                StrategyId = strategyId
            });

            return analyticalOutputs;
        }

        /// <summary>
        /// Get desk review responses statuses
        /// </summary>
        /// <param name="assessmentId">Required to fetch desk review</param>           
        /// <returns>List of desk review response statuses</returns>
        public async Task<IEnumerable<int>> GetResponseStatusesAsync(Guid assessmentId, Guid strategyId)
        {
            string query = $@"SELECT
                                       ISNULL(ADRR.Status,0) As Status                                       
                                       FROM {MalariaSchemas.ScopeDefinition}.Assessment A                                  
                                       INNER JOIN {MalariaSchemas.ScopeDefinition}.AssessmentStrategy AST ON A.Id = AST.AssessmentId AND AST.StrategyId = @StrategyId
                                       INNER JOIN {MalariaSchemas.ScopeDefinition}.[AssessmentIndicator] AI ON AI.AssessmentId = A.Id
                                       INNER JOIN {MalariaSchemas.Internal}.IndicatorStrategyMapping ISM ON AI.IndicatorId = ISM.IndicatorId AND AST.StrategyId = ISM.StrategyId
                                       INNER JOIN {MalariaSchemas.Internal}.Indicator I on I.Id = ISM.IndicatorId
                                       LEFT JOIN {MalariaSchemas.DeskReview}.AssessmentDRResponse ADRR ON ADRR.AssessmentIndicatorId = AI.Id AND ADRR.AssessmentStrategyId = AST.Id                                                                         
                                       WHERE A.Id = @AssessmentId and ISM.IndicatorPriority =@IndicatorPriority and I.Sequence not like '1.2%'";

            IEnumerable<int> deskReviewResponseStatuses = await _dbManager.QueryAsync<int>(query, new
            {
                AssessmentId = assessmentId,
                IndicatorPriority = (int)Priority.Priority,
                StrategyId = strategyId
            });

            return deskReviewResponseStatuses;
        }

    }
}

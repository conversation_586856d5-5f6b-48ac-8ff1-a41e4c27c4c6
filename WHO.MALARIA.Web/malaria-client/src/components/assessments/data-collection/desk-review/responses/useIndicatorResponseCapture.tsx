import React, { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useLocation } from "react-router";
import { useNavigate } from "react-router-dom";
import i18next from "../../../../../i18n";
import { Constants } from "../../../../../models/Constants";
import { DeskReviewRequestModel } from "../../../../../models/DeskReview/DeskReviewRequestModel";
import { KeyValuePair } from "../../../../../models/DeskReview/KeyValueType";
import { DeskReviewAssessmentResponseStatus, StatusCode } from "../../../../../models/Enums";
import { ErrorModel } from "../../../../../models/ErrorModel";
import { cleanup } from "../../../../../redux/ducks/error";
import { assessmentService } from "../../../../../services/assessmentService";
import { notificationService } from "../../../../../services/notificationService";
import { UtilityHelper } from "../../../../../utils/UtilityHelper";
import useFormChangeTracker from "../../../../common/useFormChangeTracker";

/** types of controls */
enum ControlType {
    Radio = "radio",
    Checkbox = "checkbox",
    TextBox = "text",
    Textarea = "textarea",
    Number = "number",
    Date = "date",
}

/** Custom hook to handle all response capturing activities
 * @param responseModel object of type uknown
 * @param validateFn A validate function which will be called on response change
 */
function useIndicatorResponseCapture<T extends unknown>(
    responseModel: T,
    validateFn: ((response: any) => void) | null = null
) {
    const navigate = useNavigate();
    const location: any = useLocation();
    const assessmentId = location?.state?.assessmentId;
    const indicatorId = location?.state?.indicatorId;
    const strategyId = location?.state?.strategyId;
    const sequence = location?.state?.sequence;
    const assessmentIndicatorId: string = location?.state?.assessmentIndicatorId;
    const assessmentStrategyId: string = location?.state?.assessmentStrategyId;
    const [response, setResponse] = useState<T>(responseModel) as any;
    const [deletedDocumentIds, setDeletedDocumentIds] = useState<Array<string>>([]);
    const finalizeButtonClickRef = useRef<boolean>(false);
    const dispatch = useDispatch();
    const { setIsDirtyToFalse } = useFormChangeTracker();

    //Validate the response when it is changed and finalize button is clicked
    useEffect(() => {
        if (validateFn && finalizeButtonClickRef.current) {
            validateFn(response);
        }
    }, [response]);

    //To validate the response on change event, system needs to know whether the finalized button is clicked or
    //not therefore this function will be called from the finalize button click event.
    const setTrueFlagOnFinalizeButtonClick = () =>
        (finalizeButtonClickRef.current = true);

    /** Calls an API to fetch the response */
    const getResponse = () => {
        if (!assessmentIndicatorId || !assessmentStrategyId) {
            return;
        }
        assessmentService
            .getIndicatorResponse(assessmentIndicatorId, assessmentStrategyId)
            .then((data: T) => {
                // check if server doesn't return an empty object then fill up with real object
                if (Object.keys(data as any).length > 0) {
                    setResponse(() => data);
                }
            });
    };

    /**Get response documents of an indicator */
    const getResponseDocuments = () => {
        if (!assessmentIndicatorId || !assessmentStrategyId) {
            return;
        }

        assessmentService
            .getResponseDocuments(assessmentIndicatorId, assessmentStrategyId)
            .then(data => {
                if (data) {
                    setResponse((prevState: any) => ({
                        ...prevState,
                        documents: data,
                    }))
                }
            });
    };

    /** Calls an API to fetch the parent indicator response */
    const getParentResponse = () => {
        //get the response for current indicator
        getResponse();
        const indicatorId: string = location?.state?.indicatorId;
        const assessmentId: string = location?.state?.assessmentId;

        if (!indicatorId || !assessmentId) {
            return;
        }
        assessmentService
            .getParentIndicatorResponse(
                indicatorId,
                assessmentId,
                assessmentStrategyId
            )
            .then((data: T) => {
                // check if server doesn't return an empty object then fill up with real object
                if (Object.keys(data as any).length > 0) {
                    setResponse((prevState: any) => ({
                        ...prevState,
                        ["parentData"]: data,
                    }));
                }
            });
    };

    /** Triggers whenever user selects cannot be assessed */
    const onCannotBeAssessed = (evt: React.ChangeEvent<HTMLInputElement>) => {
        setResponse({
            ...response,
            cannotBeAssessed: evt.currentTarget.checked,
            cannotBeAssessedReason: !evt.currentTarget.checked
                ? null
                : response.cannotBeAssessedReason,
        });
    };

    /** Update the local state with field/value mapping */
    const onValueChange = (field: string, value: any) => {
        setResponse((prevState: any) => ({
            ...prevState,
            [field]: value,
        }));
    };

    /** Triggers whenever HTMLInputElement change event is triggered */
    const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
        const name: string = evt.currentTarget.name;
        const type: string = evt.currentTarget.type;
        let value: string | number | null | boolean = "" || false;
        switch (type) {
            case ControlType.Radio:
                value = evt.currentTarget.value === "true" ? true : false;
                break;
            case ControlType.Checkbox:
                value = evt.currentTarget.checked;
                break;
            case ControlType.TextBox:
                value = evt.currentTarget.value;
                break;
            case ControlType.Textarea:
                value = evt.currentTarget.value;
                break;
            case ControlType.Number:
                value = isNaN(parseInt(evt.currentTarget.value))
                    ? null
                    : (+evt.currentTarget.value);
                break;
        }
        setResponse({
            ...response,
            [name]: value,
        });
    };

    /** Triggers whenever a change event if fired from HTMLInputElement for an index
     * eg. in HTML Table iteratable control can pass index to identify the index of an object which
     * needs to be modified
     * @param objProperty A property which needs to be updated within an object
     * @param index A number value of an index which needs to be updated
     */
    const onChangeWithIndex = (
        evt: React.ChangeEvent<HTMLInputElement>,
        objProperty: string,
        index: number
    ) => {
        const _response = response[objProperty];
        const type: string = evt.currentTarget.type;
        let value: string | boolean | number = "" || false || 0;

        switch (type) {
            case ControlType.Radio:
                value = evt.currentTarget.value === "true" ? true : false;
                break;
            case ControlType.Checkbox:
                value = evt.currentTarget.checked;
                break;
            case ControlType.TextBox:
            case ControlType.Textarea:
                value =
                    evt.currentTarget.value.trim() === "" ? "" : evt.currentTarget.value;
                break;
            case ControlType.Number:
                value = +evt.currentTarget.value;
                break;
        }

        _response[index][evt.currentTarget.name] = value;
        setResponse({ ...response, [objProperty]: _response });
    };

    /** Triggers whenever a change event if fired from HTMLInputElement for an index
     * eg. in HTML Table iteratable control can pass index to identify the index of an object which
     * needs to be modified
     * @param objProperty A property which needs to be updated within an object
     * @param index A number value of an index which needs to be updated
     */
    const onChangeOfArrayWithIndex = (
        evt: React.ChangeEvent<HTMLInputElement>,
        objProperty: string,
        index: number
    ) => {
        const _response = { ...response[objProperty] };
        const type: string = evt.currentTarget.type;
        let value: string | number | null | boolean = "" || false;

        switch (type) {
            case ControlType.Radio:
                value = evt.currentTarget.value === "true" ? true : false;
                break;
            case ControlType.Checkbox:
                value = evt.currentTarget.checked;
                break;
            case ControlType.TextBox:
            case ControlType.Textarea:
                value = evt.currentTarget.value;
                break;
            case ControlType.Number:
                value = isNaN(parseInt(evt.currentTarget.value))
                    ? null
                    : (+evt.currentTarget.value);
                break;
        }
        _response[index][evt.currentTarget.name] = value;
        setResponse({ ...response, [objProperty]: Object.values(_response) });
    };

    /** Triggers whenever a change event if fired from HTMLInputElement for an index
     * eg. in HTML Table iteratable control can pass index to identify the index of an object which
     * needs to be modified
     * @param objProperty A property which needs to be updated within an object
     * @param index A number value of an index which needs to be updated
     */
    const onChangeInArrayWithIndex = (
        evt: React.ChangeEvent<HTMLInputElement>,
        objProperty: string,
        index: number
    ) => {
        const _response = { ...response[objProperty] };
        const type: string = evt.currentTarget.type;
        let value: string | boolean | number = "" || false || 0;

        switch (type) {
            case ControlType.Radio:
                value = evt.currentTarget.value === "true" ? true : false;
                break;
            case ControlType.Checkbox:
                value = evt.currentTarget.checked;
                break;
            case ControlType.TextBox:
            case ControlType.Textarea:
                value = evt.currentTarget.value;
                break;
            case ControlType.Number:
                value = +evt.currentTarget.value;
                break;
        }
        _response[index] = value;
        setResponse({ ...response, [objProperty]: Object.values(_response) });
    };

    /** Triggers whenever a change event if fired from HTMLInputElement for an object property
     * eg. in HTML Table iteratable control can pass object property to identify the object which
     * needs to be modified
     * @param objProperty A property which needs to be updated within an object
     */
    const onChangeWithKey = (
        evt: React.ChangeEvent<HTMLInputElement>,
        objProperty: string
    ) => {
        const _response = { ...response[objProperty] };
        const type: string = evt.currentTarget.type;
        let value: string | number | null | boolean = "" || false;

        switch (type) {
            case ControlType.Radio:
                value = evt.currentTarget.value === "true" ? true : false;
                break;
            case ControlType.Checkbox:
                value = evt.currentTarget.checked;
                break;
            case ControlType.TextBox:
            case ControlType.Textarea:
                value = evt.currentTarget.value;
                break;
            case ControlType.Number:
                value = isNaN(parseInt(evt.currentTarget.value))
                    ? null
                    : (+evt.currentTarget.value);
                break;
        }
        _response[evt.currentTarget.name] = value;
        setResponse((prevState: any) => ({ ...prevState, [objProperty]: _response }));
    };

    /** Triggers whenever a change event if fired from HTMLInputElement for an object property
     * eg. in HTML Table iteratable control can pass object property to identify the object which
     * needs to be modified
     * @param objProperty A property which needs to be updated within an object
     */
    const onChangeWithKeyInArray = (
        evt: React.ChangeEvent<HTMLInputElement>,
        objProperty: string,
        index: number,
        keyProperty: string,
        valueProperty: string
    ) => {
        const _response = { ...response[objProperty] };
        const type: string = evt.currentTarget.type;
        let value: string | boolean | number = "" || false || 0;

        switch (type) {
            case ControlType.Radio:
                value = evt.currentTarget.value === "true" ? true : false;
                break;
            case ControlType.Checkbox:
                value = evt.currentTarget.checked;
                break;
            case ControlType.TextBox:
            case ControlType.Textarea:
                value = evt.currentTarget.value;
                break;
            case ControlType.Number:
                value = +evt.currentTarget.value;
                break;
        }

        _response[index] = {
            [keyProperty]: evt.currentTarget.name,
            [valueProperty]: value,
        };
        setResponse({ ...response, [objProperty]: Object.assign([], _response) });
    };

    /** Triggers whenever user clicks on save button */
    const onSave = () => {
        //The validations are not going to perform on save of response therefore on change validation is also not required hence we are setting
        //this flag's value as false so that on change validation will not trigger in the useEffect.        
        finalizeButtonClickRef.current = false;
        onSaveFinalize(false);
        dispatch(cleanup());

        //Tracking indicator save event in analytics
        UtilityHelper.onEventAnalytics("Desk review", "Save", "Save");
        setIsDirtyToFalse();
    };

    /** Triggers whenever user clicks on Finalize button */
    const onFinalize = () => {
        onSaveFinalize(true);

        //Tracking indicator finalize event in analytics
        UtilityHelper.onEventAnalytics("Desk review", "Finalize", "Finalize");
        setIsDirtyToFalse();
    };

    /** Triggers whenever user clicks on Finalize button */
    const onSaveFinalize = (isFinalize: boolean) => {
        const status = isFinalize
            ? DeskReviewAssessmentResponseStatus.Completed
            : DeskReviewAssessmentResponseStatus.InProgress;

        assessmentService.saveIndicatorResponse(
            new DeskReviewRequestModel(
                assessmentId,
                indicatorId,
                strategyId,
                assessmentIndicatorId,
                assessmentStrategyId,
                status,
                JSON.stringify(response),
                sequence
            )
        ).then((data: boolean) => {
            if (data && isFinalize) {
                //checks if isFinalize is true and if user refreshes/reloads the page, we're setting the location's state along with assessmentResponseStatus with this route
                //assessmentResponseStatus is needed once user clicks on finalize button to hide save button from response container to not allow to click save once indicator is finalized.    
                navigate("/assessment/data-collection/desk-review/indicator/response", { state: {
                    ...location.state,
                    assessmentResponseStatus: DeskReviewAssessmentResponseStatus.Completed
                } })
            }
        })
    };

    /**
     * Add deleted document id
     * @param id Document id GUID
     */
    const addDeletedDocumentIds = (id: string) => setDeletedDocumentIds((prevState: Array<string>) => [...prevState, id]);

    /**
     * Save indicator response and uploaded images/files.
     * @param documents An array of uploaded files.
     * @param status A response's status - InProgress | Completed
     */
    const saveResponseAndFiles = (documents: Array<KeyValuePair<string, File>>, status: DeskReviewAssessmentResponseStatus) => {
        if (status === DeskReviewAssessmentResponseStatus.InProgress) {
            finalizeButtonClickRef.current = false;
        }

        const formData = new FormData();
        formData.append("assessmentId", assessmentId);
        formData.append("indicatorId", indicatorId);
        formData.append("strategyId", strategyId);
        formData.append("assessmentIndicatorId", assessmentIndicatorId);
        formData.append("assessmentStrategyId", assessmentStrategyId);
        formData.append("status", status.toString());

        //Remove file contents from response to reduce the overall size of the request body
        const _response = { ...response };
        delete _response["documents"];
        formData.append("response", JSON.stringify(_response));

        if (documents) {
            const files = documents.filter(d => d.key && d.value);

            const maxRequestSize: number = files.reduce((prev, cur) => (prev += cur.value.size), 0);
            if (maxRequestSize > Constants.Common.MaxFileSize) {
                notificationService.sendMessage(new ErrorModel(
                    i18next.t("NotificationMessages.MaxRequestSize"),
                    StatusCode.PreConditionFailed
                ));
                return false;
            }

            for (let index = 0; index < files.length; index++) {
                formData.append(`documents[${index}].key`, files[index].key);
                formData.append(`documents[${index}].value`, files[index].value);
            }
        }

        // const deletedDocumentIds = response?.deletedDocumentIds;
        if (!deletedDocumentIds.some((id: string) => !id)) {
            for (let index = 0; index < deletedDocumentIds.length; index++) {
                formData.append(`deletedDocumentIds[${index}]`, deletedDocumentIds[index]);
            }
        }

        assessmentService.saveIndicatorResponseWithFiles(formData, sequence);
        setIsDirtyToFalse();
        return true;
    };

    //convert date to UTC to avoid issue while stringify json
    const convertToUTC = (inputDate: Date) => {
        return new Date(Date.UTC(inputDate.getFullYear(), inputDate.getMonth(), inputDate.getDate(), inputDate.getHours(), inputDate.getMinutes()))
    }

    /** Triggers whenever a change event if fired from HTMLInputElement for an index
     * eg. in HTML Table iteratable control can pass index to identify the index of an object which
     * needs to be modified
     * @param objProperty A property which needs to be updated within an object
     * @param objName A control which needs to be updated within an object
     * @param objValue A value of control which needs to be update for control
     * @param index A number value of an index which needs to be updated
     */
    const onChangeInArrayWithIndexForProperty = (
        objProperty: string,
        objName: string,
        objValue: Date | null,
        index: number
    ) => {
        const _response = { ...response[objProperty] };
        _response[index][objName] = objValue;
        setResponse({ ...response, [objProperty]: Object.values(_response) });
    };

    /**
     * Set default values of the response object which are on the root level.
     * @param propertyNames Array of string, must contain the property names of the response model.
     * @param value Default value, must be a class's object or null.
     */
    function setDefaultValue<T extends object>(
        propertyNames: Array<string>,
        value: T | null
    ) {
        const currentResponse = { ...response };
        Object.keys(response)
            .filter((key: string) => propertyNames.includes(key))
            .forEach((keyName) => (currentResponse[keyName] = value));
        setResponse(currentResponse);
    }

    return {
        response,
        setDefaultValue,
        getResponse,
        onCannotBeAssessed,
        onChange,
        onChangeWithIndex,
        onChangeOfArrayWithIndex,
        onChangeInArrayWithIndex,
        onValueChange,
        onChangeWithKey,
        onSave,
        onFinalize,
        getParentResponse,
        onChangeWithKeyInArray,
        onChangeInArrayWithIndexForProperty,
        setTrueFlagOnFinalizeButtonClick,
        saveResponseAndFiles,
        getResponseDocuments,
        addDeletedDocumentIds,
        convertToUTC
    };
}

export default useIndicatorResponseCapture;
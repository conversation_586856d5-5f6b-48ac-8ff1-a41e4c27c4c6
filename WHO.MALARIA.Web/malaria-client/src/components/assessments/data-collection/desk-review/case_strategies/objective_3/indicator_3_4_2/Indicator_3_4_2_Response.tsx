﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import { useEffect, useRef, ChangeEvent, useState } from "react";
import TextBox from "../../../../../../controls/TextBox";
import Checkbox from "../../../../../../controls/Checkbox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useTranslation } from "react-i18next";
import TableFooter from "../../../responses/TableFooter";
import parse from "html-react-parser";
import {
  Response_1,
  TransmitMalariaIndicator,
} from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_4_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import useCalculation from "../../../responses/useCalculation";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { ChecklistIndicatorModel } from "../../../../../../../models/DeskReview/ChecklistIndicatorModel";
import useChecklistIndicator from "../../../responses/useChecklistIndicator";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 3.4.2  */
function Indicator_3_4_2_Response() {
  const { t } = useTranslation(["indicators-responses"]);
  const { calculatePercentageOfYesNo } = useCalculation();
  document.title = t(
    "indicators-responses:app:DR_Objective_3_Indicator_3_4_2_Title"
  );
  const location: any = useLocation();
  const strategyId: string = location?.state?.strategyId;
  const checkListIndicators = useChecklistIndicator(strategyId);
  const [filteredCheckListIndicators, setFilteredCheckListIndicators] =
    useState<Array<ChecklistIndicatorModel>>([]);

  const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

  const validate = useFormValidation(validationRulesRef.current);

  const {
    response,
    onChange,
    onCannotBeAssessed,
    onSave,
    onFinalize,
    getResponse: originalGetResponse,
    onValueChange,
    setTrueFlagOnFinalizeButtonClick,
  } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

  // Custom getResponse that ensures hasPVivaxCases is preserved
  const getResponse = () => {
    originalGetResponse();
  };

  const errors = useSelector((state: any) => state.error);

  // Variable for checking the condition for Proportional Calculation Rate the Rate should be between 0 to 100
  let isProportionRateValid: boolean = true;

  // triggers on click of finalize button, performs validations and then action is performed
  const onResponseFinalize = () => {
    console.log("=== FINALIZING RESPONSE ===");
    console.log("hasPVivaxCases:", response.hasPVivaxCases);
    console.log("JSON being sent:", JSON.stringify(response));
    setTrueFlagOnFinalizeButtonClick();
    const isFormValid = validate(response);
    if (isFormValid && isProportionRateValid) {
      onFinalize();
    }
  };

  // triggers on click of save button
  const onResponseSave = () => {
    console.log("=== SAVING RESPONSE ===");
    console.log("hasPVivaxCases:", response.hasPVivaxCases);
    console.log("JSON being sent:", JSON.stringify(response));
    onSave();
  };

  useEffect(() => {
    getResponse();
  }, []);

  // Ensure hasPVivaxCases is initialized if it's undefined
  useEffect(() => {
    if (response.hasPVivaxCases === undefined) {
      console.log("Initializing hasPVivaxCases from undefined to null");
      onValueChange("hasPVivaxCases", null);
    }
  }, [response]);

  // Debug: Log the response after getResponse is called
  useEffect(() => {
    console.log("=== RESPONSE LOADED ===");
    console.log("hasPVivaxCases:", response.hasPVivaxCases);
    console.log("typeof hasPVivaxCases:", typeof response.hasPVivaxCases);
    console.log("Full response:", response);
  }, [response]);

  useEffect(() => {
    if (checkListIndicators) {
      onValueChange("checkListIndicatorsCount", checkListIndicators.length);
    }
  }, [checkListIndicators]);

  useEffect(() => {
    validationRulesRef.current =
      response?.cannotBeAssessed === true
        ? CannotBeAssessedReasonValidationRule
        : ValidationRules;
  }, [response?.cannotBeAssessed]);

  // Filter indicators based on P.vivax cases selection
  useEffect(() => {
    if (checkListIndicators) {
      let filteredIndicators = [...checkListIndicators];

      // P.vivax related indicator IDs that should be hidden when hasPVivaxCases is false
      const pVivaxIndicatorIds = [
        "114ee26b-cd72-41b4-aff1-794905bf0f97", // "Proportion of P.vivax cases"
        "12065490-6424-40eb-a029-20d500dcc837", // "Proportion of P.vivax cases treated with primaquine"
      ];

      if (response.hasPVivaxCases === false) {
        // Filter out P.vivax related indicators
        filteredIndicators = checkListIndicators.filter(
          indicator => !pVivaxIndicatorIds.includes(indicator.id)
        );

        // Remove P.vivax indicators from transmitMalariaIndicators
        const filteredTransmitMalariaIndicators =
          response.transmitMalariaIndicators?.filter(
            transmitIndicator =>
              !pVivaxIndicatorIds.includes(
                transmitIndicator.checklistIndicatorId
              )
          ) || [];

        onValueChange(
          "transmitMalariaIndicators",
          filteredTransmitMalariaIndicators
        );
      }

      setFilteredCheckListIndicators(filteredIndicators);
      onValueChange("checkListIndicatorsCount", filteredIndicators.length);
    }
  }, [checkListIndicators, response.hasPVivaxCases]);

  //Triggers onChange of cannotBeAssessed checkbox
  const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
    //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
    //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
    //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
    //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
    validationRulesRef.current = evt.currentTarget.checked
      ? CannotBeAssessedReasonValidationRule
      : ValidationRules;

    onCannotBeAssessed(evt);
  };

  // Triggered whenever the has pVivax cases are updated
  const onPVivaxCasesChangeHandler = (
    fieldName: string,
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    onValueChange(fieldName, e.target.value === "true" ? true : false);
  };

  // Triggered whenever the control values are changed and update response
  const onRadioButtonValueChange = (
    fieldName: string,
    value: any,
    indicatorId: string
  ) => {
    value = value === "false" ? false : true;
    const transmitMalariaIndicators = response.transmitMalariaIndicators
      ? [...response.transmitMalariaIndicators]
      : [];
    const indicatorData: TransmitMalariaIndicator | undefined =
      transmitMalariaIndicators.find(
        (v: TransmitMalariaIndicator) => v.checklistIndicatorId === indicatorId
      );

    if (indicatorData) {
      indicatorData[fieldName] = value;
    } else {
      const transmitMalariaIndicator = new TransmitMalariaIndicator(
        indicatorId
      );
      transmitMalariaIndicators.push({
        ...transmitMalariaIndicator,
        [fieldName]: value,
      });
    }

    onValueChange("transmitMalariaIndicators", transmitMalariaIndicators);
  };

  const headersMain = [
    {
      field: "indicators",
      label: `${t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:No."
      )}
            ${t(
              "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:Indicators"
            )}`,
    },
    {
      field: "indicatorMonitoredDocuments",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:IndicatorMonitoredInRoutineOutputs"
      ),
    },
    {
      field: "disagregation",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Disagregation"
      ),
    },
    { field: "", label: "" },
    { field: "", label: "" },
    { field: "", label: "" },
    { field: "", label: "" },
    { field: "", label: "" },
    { field: "", label: "" },
    { field: "", label: "" },
  ];
  const headerSecondary = [
    { field: "", label: " " },
    { field: " ", label: "" },
    {
      field: "under5",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Under5"
      ),
    },
    {
      field: "over5",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Over5"
      ),
    },
    {
      field: "gender",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Sex"
      ),
    },
    {
      field: "pregnantWoman",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:PregnantWoman"
      ),
    },
    {
      field: "healthSector",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:HealthSector"
      ),
    },
    {
      field: "geography",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Geography"
      ),
    },
    {
      field: "methodOfConfirmation",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:MethodOfConfirmation"
      ),
    },
    {
      field: "other",
      label: t(
        "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Other"
      ),
    },
  ];

  //Method creates an array of properties that have checked "Yes"
  const calculateCheckedForProperty = () => {
    const indicatorsToUse =
      filteredCheckListIndicators.length > 0
        ? filteredCheckListIndicators
        : checkListIndicators;
    const propertyArray: boolean[] = indicatorsToUse?.map(
      (checkListIndicator: ChecklistIndicatorModel) =>
        getTransmitMalariaIndicatorValue(
          checkListIndicator.id,
          "indicatorMonitored"
        )
    );

    return calculatePercentageOfYesNo(propertyArray);
  };

  // get the TransmitMalariaIndicator based on the indicatorId and bind it on change event
  const getTransmitMalariaIndicatorValue = (
    indicatorId: string,
    fieldName: string
  ) => {
    const transmitIndicator = response.transmitMalariaIndicators?.find(
      (v: TransmitMalariaIndicator) => v.checklistIndicatorId === indicatorId
    );
    if (!transmitIndicator || transmitIndicator[fieldName] === undefined)
      return "";

    return transmitIndicator[fieldName];
  };

  //Check condition for met and not met and return status
  const getMetNotMetStatus = () => {
    const recordedVariablesPercentage = calculateCheckedForProperty();

    onValueChange(
      "metNotMetStatus",
      recordedVariablesPercentage >= 80
        ? MetNotMetEnum.Met
        : recordedVariablesPercentage < 50
          ? MetNotMetEnum.NotMet
          : MetNotMetEnum.PartiallyMet
    );
  };

  useEffect(() => {
    getMetNotMetStatus();
  }, [filteredCheckListIndicators, checkListIndicators]);

  //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100
  const calculateCheckedProperty = () => {
    const proportionRateValue = calculateCheckedForProperty();
    const proportionRateExceptionContent = (
      <span className='Mui-error d-flex mb-2'>
        * {t("indicators-responses:Common:ResponseProportionError")}
      </span>
    );

    if (proportionRateValue >= 0 && proportionRateValue <= 100) {
      isProportionRateValid = true;
      return proportionRateValue + "%";
    }

    isProportionRateValid = false;
    return proportionRateExceptionContent;
  };

  return (
    <>
      <MetNotMetStatus
        status={response.metNotMetStatus}
        tooltip={t(
          "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:MetNotMetTooltip"
        )}
      />
      <div className='response-assess-wrapper'>
        <Checkbox
          id='cannotBeAssessed'
          name='cannotBeAssessed'
          label={t("indicators-responses:Common:IndicatorNoAssess")}
          onChange={onCannotBeAssessedChange}
          checked={response?.cannotBeAssessed}
        />
      </div>

      {!response?.cannotBeAssessed ? (
        <div className='response-wrapper'>
          <p>
            {t(
              "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:ResponseDesc"
            )}
          </p>
          <div>
            {/*Show error message if user does not select 'Yes' or 'No' for 'Indicator Checklist' and 'Disaggregations' for variables*/}
            {!!Object.keys(errors).length && (
              <span className='Mui-error d-flex mb-2'>
                *{" "}
                {t(
                  "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:ResponseError"
                )}
              </span>
            )}

            <div className='row'>
              <div className='col-xs-12 col-md-12 mb-3'>
                <label>
                  {parse(
                    t(
                      "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:HasPVivaxCasesQuestion"
                    )
                  )}
                </label>
                <RadioButtonGroup
                  id='hasPVivaxCases'
                  name='hasPVivaxCases'
                  row
                  color='primary'
                  options={[
                    new MultiSelectModel(
                      true,
                      t("indicators-responses:Common:Yes")
                    ),
                    new MultiSelectModel(
                      false,
                      t("indicators-responses:Common:No")
                    ),
                  ]}
                  value={response.hasPVivaxCases}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    onPVivaxCasesChangeHandler("hasPVivaxCases", e)
                  }
                  error={errors["hasPVivaxCases"] && errors["hasPVivaxCases"]}
                  helperText={
                    errors["hasPVivaxCases"] && errors["hasPVivaxCases"]
                  }
                />
              </div>
            </div>

            <Table>
              <>
                <TableHeader
                  headers={headersMain.map((header: any) => header.label)}
                />
                <TableHeader
                  headers={headerSecondary.map((header: any) => header.label)}
                />
                <TableBody>
                  <>
                    {(filteredCheckListIndicators.length > 0
                      ? filteredCheckListIndicators
                      : checkListIndicators
                    )?.map(
                      (
                        checkListIndicator: ChecklistIndicatorModel,
                        index: number
                      ) => (
                        <TableRow key={`row_${checkListIndicator.id}_${index}`}>
                          <>
                            <TableCell>
                              <>
                                <p className='d-flex mb-0'>
                                  <span className='d-inline-flex me-2'>
                                    {index + 1}{" "}
                                  </span>{" "}
                                  {checkListIndicator.name}
                                </p>
                              </>
                            </TableCell>
                            <TableCell>
                              <RadioButtonGroup
                                id='indicatorMonitored'
                                name='indicatorMonitored'
                                row
                                color='primary'
                                options={[
                                  new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes"),
                                    !checkListIndicator.indicatorMonitored
                                  ),
                                  new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No"),
                                    !checkListIndicator.indicatorMonitored
                                  ),
                                ]}
                                value={getTransmitMalariaIndicatorValue(
                                  checkListIndicator.id,
                                  "indicatorMonitored"
                                )}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) => {
                                  onRadioButtonValueChange(
                                    "indicatorMonitored",
                                    e.currentTarget.value,
                                    checkListIndicator.id
                                  );
                                  getMetNotMetStatus();
                                }}
                                error={
                                  errors[
                                    `transmitMalariaIndicators[${index}].indicatorMonitored`
                                  ] &&
                                  errors[
                                    `transmitMalariaIndicators[${index}].indicatorMonitored`
                                  ]
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <RadioButtonGroup
                                id='underFive'
                                name='underFive'
                                color='primary'
                                options={[
                                  new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes"),
                                    !checkListIndicator.underFive
                                  ),
                                  new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No"),
                                    !checkListIndicator.underFive
                                  ),
                                ]}
                                value={getTransmitMalariaIndicatorValue(
                                  checkListIndicator.id,
                                  "underFive"
                                )}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) =>
                                  onRadioButtonValueChange(
                                    "underFive",
                                    e.currentTarget.value,
                                    checkListIndicator.id
                                  )
                                }
                                error={
                                  errors[
                                    `transmitMalariaIndicators[${index}].underFive`
                                  ] &&
                                  errors[
                                    `transmitMalariaIndicators[${index}].underFive`
                                  ]
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <RadioButtonGroup
                                id='overFive'
                                name='overFive'
                                row
                                color='primary'
                                options={[
                                  new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes"),
                                    !checkListIndicator.overFive
                                  ),
                                  new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No"),
                                    !checkListIndicator.overFive
                                  ),
                                ]}
                                value={getTransmitMalariaIndicatorValue(
                                  checkListIndicator.id,
                                  "overFive"
                                )}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) =>
                                  onRadioButtonValueChange(
                                    "overFive",
                                    e.currentTarget.value,
                                    checkListIndicator.id
                                  )
                                }
                                error={
                                  errors[
                                    `transmitMalariaIndicators[${index}].overFive`
                                  ] &&
                                  errors[
                                    `transmitMalariaIndicators[${index}].overFive`
                                  ]
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <RadioButtonGroup
                                id='gender'
                                name='gender'
                                color='primary'
                                options={[
                                  new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes"),
                                    !checkListIndicator.gender
                                  ),
                                  new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No"),
                                    !checkListIndicator.gender
                                  ),
                                ]}
                                value={getTransmitMalariaIndicatorValue(
                                  checkListIndicator.id,
                                  "gender"
                                )}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) =>
                                  onRadioButtonValueChange(
                                    "gender",
                                    e.currentTarget.value,
                                    checkListIndicator.id
                                  )
                                }
                                error={
                                  errors[
                                    `transmitMalariaIndicators[${index}].gender`
                                  ] &&
                                  errors[
                                    `transmitMalariaIndicators[${index}].gender`
                                  ]
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <RadioButtonGroup
                                id='pregnantWoman'
                                name='pregnantWoman'
                                row
                                color='primary'
                                options={[
                                  new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes"),
                                    !checkListIndicator.pregnantWoman
                                  ),
                                  new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No"),
                                    !checkListIndicator.pregnantWoman
                                  ),
                                ]}
                                value={getTransmitMalariaIndicatorValue(
                                  checkListIndicator.id,
                                  "pregnantWoman"
                                )}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) =>
                                  onRadioButtonValueChange(
                                    "pregnantWoman",
                                    e.currentTarget.value,
                                    checkListIndicator.id
                                  )
                                }
                                error={
                                  errors[
                                    `transmitMalariaIndicators[${index}].pregnantWoman`
                                  ] &&
                                  errors[
                                    `transmitMalariaIndicators[${index}].pregnantWoman`
                                  ]
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <RadioButtonGroup
                                id='healthSector'
                                name='healthSector'
                                color='primary'
                                options={[
                                  new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes"),
                                    !checkListIndicator.healthSector
                                  ),
                                  new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No"),
                                    !checkListIndicator.healthSector
                                  ),
                                ]}
                                value={getTransmitMalariaIndicatorValue(
                                  checkListIndicator.id,
                                  "healthSector"
                                )}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) =>
                                  onRadioButtonValueChange(
                                    "healthSector",
                                    e.currentTarget.value,
                                    checkListIndicator.id
                                  )
                                }
                                error={
                                  errors[
                                    `transmitMalariaIndicators[${index}].healthSector`
                                  ] &&
                                  errors[
                                    `transmitMalariaIndicators[${index}].healthSector`
                                  ]
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <RadioButtonGroup
                                id='geography'
                                name='geography'
                                row
                                color='primary'
                                options={[
                                  new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes"),
                                    !checkListIndicator.geography
                                  ),
                                  new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No"),
                                    !checkListIndicator.geography
                                  ),
                                ]}
                                value={getTransmitMalariaIndicatorValue(
                                  checkListIndicator.id,
                                  "geography"
                                )}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) =>
                                  onRadioButtonValueChange(
                                    "geography",
                                    e.currentTarget.value,
                                    checkListIndicator.id
                                  )
                                }
                                error={
                                  errors[
                                    `transmitMalariaIndicators[${index}].geography`
                                  ] &&
                                  errors[
                                    `transmitMalariaIndicators[${index}].geography`
                                  ]
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <RadioButtonGroup
                                id='confirmationMethod'
                                name='confirmationMethod'
                                color='primary'
                                options={[
                                  new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes"),
                                    !checkListIndicator.confirmationMethod
                                  ),
                                  new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No"),
                                    !checkListIndicator.confirmationMethod
                                  ),
                                ]}
                                value={getTransmitMalariaIndicatorValue(
                                  checkListIndicator.id,
                                  "confirmationMethod"
                                )}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) =>
                                  onRadioButtonValueChange(
                                    "confirmationMethod",
                                    e.currentTarget.value,
                                    checkListIndicator.id
                                  )
                                }
                                error={
                                  errors[
                                    `transmitMalariaIndicators[${index}].confirmationMethod`
                                  ] &&
                                  errors[
                                    `transmitMalariaIndicators[${index}].confirmationMethod`
                                  ]
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <RadioButtonGroup
                                id='other'
                                name='other'
                                color='primary'
                                options={[
                                  new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes"),
                                    !checkListIndicator.other
                                  ),
                                  new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No"),
                                    !checkListIndicator.other
                                  ),
                                ]}
                                value={getTransmitMalariaIndicatorValue(
                                  checkListIndicator.id,
                                  "other"
                                )}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) =>
                                  onRadioButtonValueChange(
                                    "other",
                                    e.currentTarget.value,
                                    checkListIndicator.id
                                  )
                                }
                                error={
                                  errors[
                                    `transmitMalariaIndicators[${index}].other`
                                  ] &&
                                  errors[
                                    `transmitMalariaIndicators[${index}].other`
                                  ]
                                }
                              />
                            </TableCell>
                          </>
                        </TableRow>
                      )
                    )}
                  </>
                </TableBody>

                <TableFooter>
                  <>
                    <TableCell>
                      <span>
                        {t(
                          "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:TableFooter"
                        )}
                      </span>
                    </TableCell>

                    <TableCell colSpan={9}>
                      <label>{calculateCheckedProperty()}</label>
                    </TableCell>
                  </>
                </TableFooter>
              </>
            </Table>
          </div>
        </div>
      ) : (
        <div className='response-wrapper d-flex'>
          <TextBox
            id='cannotBeAssessedReason'
            name='cannotBeAssessedReason'
            label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
            multiline
            rows={10}
            variant='outlined'
            fullWidth
            value={response?.cannotBeAssessedReason}
            onChange={onChange}
            error={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
            helperText={
              errors["cannotBeAssessedReason"] &&
              errors["cannotBeAssessedReason"]
            }
          />
        </div>
      )}
      <SaveFinalizeButton
        onSave={onResponseSave}
        onFinalize={onResponseFinalize}
      />
    </>
  );
}

export default Indicator_3_4_2_Response;

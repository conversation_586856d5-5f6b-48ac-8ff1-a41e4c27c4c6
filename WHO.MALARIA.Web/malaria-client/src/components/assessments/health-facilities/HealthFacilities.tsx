﻿import { Button } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import FileUploader from "../../controls/FileUploader";
import { questionBankService } from "../../../services/questionBankService";
import { UtilityHelper } from "../../../utils/UtilityHelper";
import { Constants } from "../../../models/Constants";
import parse from "html-react-parser";

/** HealthFacilities component to generate/upload and view template*/
const HealthFacilities = () => {
  const { t } = useTranslation();
  document.title = t("translation:app.HealthFacilitiesPageTitle");
  const [isFileSizeMaximum, setIsFileSizeMaximum] = useState<boolean>(false);
  const [isValidFileExtension, setIsValidFileExtension] =
    useState<boolean>(false);
  let selectedCountryId = sessionStorage.getItem(
    Constants.SessionStorageKey.SELECTED_COUNTRY
  );
  const [fileName, setfileName] = useState<string>();

  useEffect(() => {
    GetLastUploadedFileDetails();
  }, []);

  // call on page load set file name
  const GetLastUploadedFileDetails = () => {
    questionBankService
      .getLastUploadedFileName(selectedCountryId ?? "")
      .then((record: string) => {
        setfileName(record);
      });
  };

  // Triggers whenever user clicks on upload button
  const uploadFile = (evt: React.ChangeEvent<HTMLInputElement>) => {
    // check file size
    const filesize = evt.target.files ? evt.target.files[0].size : 0;
    if (filesize > Constants.Common.MaxFileSize) {
      //25MB
      setIsFileSizeMaximum(true);
      return;
    } else {
      setIsFileSizeMaximum(false);
    }

    // check file extension
    const fileExtension: string | undefined = evt.target.files
      ? evt.target.files[0].name.split(".").pop()
      : "";
    if (
      Constants.Common.ValidExcelExtensions.indexOf(fileExtension as string) ===
      -1
    ) {
      setIsValidFileExtension(true);
      return;
    } else {
      setIsValidFileExtension(false);
    }

    // FormData object to pass along with file so it can be mapped at server side
    const formData = new FormData();
    formData.append("File", evt.target.files ? evt.target.files[0] : "");
    formData.append("CountryId", selectedCountryId ?? "");
    questionBankService
      .uploadHealthFacilityTemplateResponse(formData)
      .then(() => {
        GetLastUploadedFileDetails();
      });
  };

  // Triggers whenever user clicks on Generate Template button
  const onGenerateTemplate = () => {
    const fileName: string =
      t("Assessment.HealthFacilities.HealthFacilityFileName") + ".xlsx";

    questionBankService.downloadHealthFacilities().then((content: any) => {
      UtilityHelper.download(content, fileName);
    });
  };

  // Triggers whenever user clicks on export/ view existing health facility button
  const exportExcel = () => {
    const fileName: string =
      t("Assessment.HealthFacilities.HealthFacilityFileName") + ".xlsx";
    questionBankService
      .exportHealthFacilityExcel(selectedCountryId ?? "")
      .then((content: any) => {
        UtilityHelper.download(content, fileName);
      });
  };

  return (
    <section className="page-full-section">
      <div className="container-fluid">
        <div className="row d-flex document-title-section">
          <div
            className="col-md-4
                        d-flex
                        justify-content-left
                        align-items-center"
          >
            <span className="fw-bold text-uppercase heading-title">
              {t("Assessment.HealthFacilities.HealthFacilitiesHeading")}
            </span>
          </div>
        </div>

        <div>
          <p>{parse(t("Assessment.HealthFacilities.HealthFacilitiesDesc"))}</p>
        </div>

        <div className="response-action-wrapper h-400 d-flex align-items-center justify-content-center">
          <div className="button-action-section d-flex justify-content-center p-3">
            <Button
              className={classNames("btn", "app-btn-secondary")}
              onClick={exportExcel}
            >
              {t("Assessment.HealthFacilities.ViewHealthFacility")}
            </Button>

            <Button
              className={classNames("btn", "app-btn-secondary")}
              onClick={onGenerateTemplate}
            >
              {t("Assessment.HealthFacilities.GenerateTemplate")}
            </Button>

            <FileUploader
              key={`fileuploader_${Math.random()}`}
              id="template"
              linkCss={classNames("btn", "app-btn-secondary", "ms-2", "upload-btn")}
              onChange={uploadFile}
              accept=".xlsx, .xls"
            >
              <div style={{ display: "grid" }}>
                {fileName && (
                  <>
                    <p className="m-0">
                      {" "}
                      <i className="d-inline-flex mx-2 align-items-center">
                        {t("Assessment.HealthFacilities.FileUploadSize")}
                      </i>
                    </p>

                    <p className="m-0">
                      <span className="d-inline-flex mx-2 align-items-center">
                        <b className="me-2">{t("Common.LastUploadedFile")}:</b>{" "}
                        {fileName}
                      </span>
                    </p>
                  </>
                )}
                {!fileName && (
                  <>
                    <i className="d-inline-flex mx-2 align-items-center">
                      {t(
                        "Assessment.DataCollection.DataQualityAssessment.FileUploadSize"
                      )}
                    </i>
                  </>
                )}
              </div>
            </FileUploader>
          </div>
        </div>
        <>
          {isFileSizeMaximum && (
            <span className="d-flex justify-content-center Mui-error">
              {" "}
              {t("Exception.InvalidFileSize")}{" "}
            </span>
          )}
          {isValidFileExtension && (
            <span className="d-flex justify-content-center Mui-error">
              {" "}
              {t("Exception.InvalidFile")}{" "}
            </span>
          )}
        </>
      </div>
    </section>
  );
};

export default HealthFacilities;

﻿**Issue: P.vivax Question Response Not Persisting in Indicator 3.4.2**

**Problem Description:**
In the WHO Malaria Surveillance Tool, specifically for Indicator 3.4.2 in burden reduction settings, the P.vivax cases question response is not being saved to the database properly. When users select "Yes" or "No" for the question "Do you have any P.vivax cases?" and save their response, the selection is lost when they return to edit the assessment.

**Steps to Reproduce:**
1. Navigate to Desk Review → Objective 3 → Indicator 3.4.2 (burden reduction strategy)
2. Click "Edit" or open the indicator in assessment mode
3. Locate the question "Do you have any P.vivax cases?"
4. Select either "Yes" or "No" using the radio buttons
5. Click the "Save" button (not "Save & Finalize")
6. Navigate away from the indicator or refresh the page
7. Return to the same indicator and click "Edit" again

**Current Behavior:**
- The P.vivax question shows no selection (neither "Yes" nor "No" is selected)
- The radio buttons appear in their default unselected state
- Console logs show `hasPVivaxCases: null` when the page loads

**Expected Behavior:**
- The previously selected answer ("Yes" or "No") should be displayed when reopening the indicator
- The radio button should show the saved selection
- The filtering logic should work correctly based on the saved selection (hiding P.vivax indicators when "No" was selected)

**Technical Context:**
- This is a data persistence issue between the frontend TypeScript model and backend C# model
- The `hasPVivaxCases` property exists in both frontend (`Response_1.tsx`) and backend (`Response_1.cs`) models
- Similar functionality works correctly in Indicator 3.3.2, which uses `Response_2` model
- The backend application may need to be rebuilt after recent model changes
- The issue affects the calculation logic since hidden indicators should be excluded from percentage calculations

**Files Involved:**
- Frontend: `WHO.MALARIA.Web/malaria-client/src/components/assessments/data-collection/desk-review/case_strategies/objective_3/indicator_3_4_2/Indicator_3_4_2_Response.tsx`
- Backend: `WHO.MALARIA.Domain/AssessmentResponseModels/DeskReview/Objective_3/Indicator_3_4_2/Response_1.cs`
- Models: `WHO.MALARIA.Web/malaria-client/src/models/DeskReview/Objective_3/Indicator_3_4_2/Response_1.tsx`
using Duende.IdentityServer.Stores;
using Microsoft.Extensions.DependencyInjection;

namespace WHO.MALARIA.Web.Areas.Idp.Helper
{
    /// <summary>
    ///  Class responsible for validating the clients registered in STS.
    /// </summary>
    public static class CustomIdentityServerBuilderExtensions
    {
        public static IIdentityServerBuilder AddCustomUserStore(this IIdentityServerBuilder builder)
        {
            builder.Services.AddSingleton<IClientStore, CustomClientStore>();
            builder.AddClientStore<CustomClientStore>();

            return builder;
        }
    }
}
